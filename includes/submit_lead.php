<?php
header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Only POST method allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
if (json_last_error() !== JSON_ERROR_NONE) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit;
}

// Validate required fields
$required_fields = [
    'transport_from', 
    'transport_to', 
    'transport_type',
    'vehicle_year',
    'vehicle_brand',
    'vehicle_model',
    'email',
    'name',
    'phone'
];

$errors = [];

foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        $errors[] = "Field '$field' is required.";
    }
}

// Validate email format
if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Invalid email format.";
}

// If validation errors, return them
if (!empty($errors)) {
    http_response_code(400);
    echo json_encode(['errors' => $errors]);
    exit;
}

// Prepare data for BATS CRM
$lead_data = [
    'first_name' => $input['name'],
    'email' => $input['email'],
    'phone' => $input['phone'],
    'transport_from' => $input['transport_from'],
    'transport_to' => $input['transport_to'],
    'transport_type' => $input['transport_type'],
    'vehicle_year' => $input['vehicle_year'],
    'vehicle_make' => $input['vehicle_brand'],
    'vehicle_model' => $input['vehicle_model'],
    'vehicle_operable' => $input['vehicle_operable'] ?? 'unknown',
    'available_date' => $input['available_date'] ?? 'ASAP',
    'source' => 'Website Form'
];

// Send to BATS CRM (replace with your actual API details)
$bats_api_url = 'https://api.batscrm.com/leads';
$bats_api_key = 'YOUR_API_KEY';

$ch = curl_init($bats_api_url);
curl_setopt_array($ch, [
    CURLOPT_POST => true,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HTTPHEADER => [
        'Content-Type: application/json'
    ],
    CURLOPT_POSTFIELDS => json_encode($lead_data)
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($http_code === 200 || $http_code === 201) {
    echo json_encode(['success' => true]);
} else {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to submit lead to BATS CRM', 'api_response' => $response]);
}
?>