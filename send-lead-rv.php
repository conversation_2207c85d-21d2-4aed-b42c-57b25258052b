<?php
session_start(); // Start the session to store data
header('Content-Type: application/json'); // Set JSON response header

// Log request for debugging
$logFile = 'rv_form_debug.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request received\n", FILE_APPEND);
file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request method: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);

// Handle direct GET requests to this file
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    echo json_encode([
        'success' => false,
        'message' => 'This endpoint requires a POST request with form data',
        'redirect' => 'rv-shipping'
    ]);
    exit;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // API Authentication
    $authKey = "23d66938-96a5-4ad4-b1b5-122c12a307ea"; // Sandbox Key

   
    // Collect form data
    $name = $_POST['name'] ?? "unknown";
    $email = $_POST['email'] ?? "unknown";
    $phone = $_POST['phone'] ?? "unknown";
    $origin = $_POST['transport_from'] ?? "unknown";
    $destination = $_POST['transport_to'] ?? "unknown";
    $vehicle_brand = $_POST['vehicle_brand'] ?? "unknown";
    $vehicle_model = $_POST['vehicle_model'] ?? "unknown";
    $vehicle_year = isset($_POST['vehicle_year']) ? (int)$_POST['vehicle_year'] : "unknown";
    $available_date = $_POST['available_date'] ?? "asap";
    $vehicle_operable = $_POST['vehicle_operable'] ?? "yes";
    $vehicle_type = $_POST['vehicle_type'] ?? "RV";

    $transport_type = ($_POST['transport_type'] === 'Open') ? 1 : 2;
    $vehicle_inop = ($vehicle_operable === 'no') ? 1 : 0;


    // Process the date - ensure it's in the correct format (mm/dd/yyyy)
    if ($available_date === "asap") {
        // If ASAP, use today's date
        $ship_date = date("m/d/Y");
    } else {
        // Otherwise use the provided date
        $ship_date = $available_date;
        
        // If the date is not in the correct format, convert it
        if (!preg_match('/^\d{2}\/\d{2}\/\d{4}$/', $ship_date)) {
            // Try to parse the date and reformat it
            $date_obj = date_create($ship_date);
            if ($date_obj) {
                $ship_date = date_format($date_obj, "m/d/Y");
            } else {
                // If parsing fails, default to today
                $ship_date = date("m/d/Y");
            }
        }
    }
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Ship date: $ship_date\n", FILE_APPEND);
    
    // Process locations to extract city, state, zip
    function extractLocationParts($location) {
        $parts = [
            'city' => '',
            'state' => '',
            'postal_code' => ''
        ];
        
        // Try to match "City, State ZIP" format
        if (preg_match('/^(.*?),\s*([A-Z]{2})(?:\s+(\d{5}))?/', $location, $matches)) {
            $parts['city'] = $matches[1];
            $parts['state'] = $matches[2];
            $parts['postal_code'] = isset($matches[3]) ? $matches[3] : '';
        } 
        // Try to match just a ZIP code
        elseif (preg_match('/^(\d{5})$/', $location, $matches)) {
            $parts['postal_code'] = $matches[1];
        }
        
        return $parts;
    }
    
    $origin_parts = extractLocationParts($origin);
    $destination_parts = extractLocationParts($destination);
    
    $origin_city = $origin_parts['city'];
    $origin_state = $origin_parts['state'];
    $origin_postal_code = $origin_parts['postal_code'];
    
    $destination_city = $destination_parts['city'];
    $destination_state = $destination_parts['state'];
    $destination_postal_code = $destination_parts['postal_code'];
    
    // If postal codes are empty, set default values (required by API)
    if (empty($origin_postal_code)) {
        // If we have a state, use a default ZIP for that state
        if (!empty($origin_state)) {
            // This is a simplified approach - in production you'd want a proper state-to-ZIP mapping
            $default_zips = [
                'NY' => '10001', 'CA' => '90001', 'TX' => '75001', 'FL' => '33101',
                'IL' => '60601', 'PA' => '19101', 'OH' => '44101', 'GA' => '30301',
                'NC' => '27601', 'MI' => '48201', 'NJ' => '07101', 'VA' => '23218',
                'WA' => '98101', 'AZ' => '85001', 'MA' => '02101', 'TN' => '37201',
                'IN' => '46201', 'MO' => '63101', 'MD' => '21201', 'WI' => '53201',
                'MN' => '55401', 'CO' => '80201', 'AL' => '35201', 'SC' => '29201',
                'LA' => '70112', 'KY' => '40201', 'OR' => '97201', 'OK' => '73101',
                'CT' => '06101', 'IA' => '50301', 'MS' => '39201', 'AR' => '72201',
                'KS' => '66601', 'UT' => '84101', 'NV' => '89101', 'NM' => '87101',
                'WV' => '25301', 'NE' => '68101', 'ID' => '83701', 'HI' => '96801',
                'ME' => '04101', 'NH' => '03101', 'RI' => '02901', 'MT' => '59601',
                'DE' => '19901', 'SD' => '57101', 'ND' => '58501', 'AK' => '99501',
                'DC' => '20001', 'VT' => '05601', 'WY' => '82001'
            ];
            
            $origin_postal_code = isset($default_zips[$origin_state]) ? $default_zips[$origin_state] : '00000';
        } else {
            $origin_postal_code = '00000'; // Default if no state
        }
    }
    
    if (empty($destination_postal_code)) {
        // Same approach for destination
        if (!empty($destination_state)) {
            $default_zips = [
                'NY' => '10001', 'CA' => '90001', 'TX' => '75001', 'FL' => '33101',
                'IL' => '60601', 'PA' => '19101', 'OH' => '44101', 'GA' => '30301',
                'NC' => '27601', 'MI' => '48201', 'NJ' => '07101', 'VA' => '23218',
                'WA' => '98101', 'AZ' => '85001', 'MA' => '02101', 'TN' => '37201',
                'IN' => '46201', 'MO' => '63101', 'MD' => '21201', 'WI' => '53201',
                'MN' => '55401', 'CO' => '80201', 'AL' => '35201', 'SC' => '29201',
                'LA' => '70112', 'KY' => '40201', 'OR' => '97201', 'OK' => '73101',
                'CT' => '06101', 'IA' => '50301', 'MS' => '39201', 'AR' => '72201',
                'KS' => '66601', 'UT' => '84101', 'NV' => '89101', 'NM' => '87101',
                'WV' => '25301', 'NE' => '68101', 'ID' => '83701', 'HI' => '96801',
                'ME' => '04101', 'NH' => '03101', 'RI' => '02901', 'MT' => '59601',
                'DE' => '19901', 'SD' => '57101', 'ND' => '58501', 'AK' => '99501',
                'DC' => '20001', 'VT' => '05601', 'WY' => '82001'
            ];
            
            $destination_postal_code = isset($default_zips[$destination_state]) ? $default_zips[$destination_state] : '00000';
        } else {
            $destination_postal_code = '00000'; // Default if no state
        }
    }
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Processed locations\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Origin: $origin_city, $origin_state, $origin_postal_code\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Destination: $destination_city, $destination_state, $destination_postal_code\n", FILE_APPEND);

     $vehicles = [
        [
            "vehicle_inop" => $vehicle_inop,
            "vehicle_make" => $vehicle_brand,
            "vehicle_model" => $vehicle_model,
            "vehicle_model_year" => $vehicle_year,
            "vehicle_type" => "RV",
            "type" => "RV"
        ]
    ];

    $data = [
        "AuthKey" => $authKey,
        "first_name" => $name,
        "last_name" => "",
        "email" => $email,
        "phone" => preg_replace('/[^0-9]/', '', $phone),
        "comment_from_shipper" => "RV Transport",
        "origin_city" => $origin_city,
        "origin_state" => $origin_state,
        "origin_postal_code" => $origin_postal_code, // Ensure this is not empty
        "origin_country" => "US",
        "destination_city" => $destination_city,
        "destination_state" => $destination_state,
        "destination_postal_code" => $destination_postal_code, // Ensure this is not empty
        "destination_country" => "US",
        "ship_date" => $ship_date, // Ensure this is in the correct format
        "transport_type" => $transport_type,
        "vehicle_type" => "RV", // Add this at the root level
        "Vehicles" => $vehicles
    ];
    
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Prepared API data\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - API data: " . json_encode($data, JSON_PRETTY_PRINT) . "\n", FILE_APPEND);

    // Store data in session for thank-you.php
    $_SESSION['name'] = $name;
    $_SESSION['email'] = $email;
    $_SESSION['phone'] = $phone;
    $_SESSION['transport_from'] = $origin;
    $_SESSION['transport_to'] = $destination;
    $_SESSION['vehicle_brand'] = $vehicle_brand;
    $_SESSION['vehicle_model'] = $vehicle_model;
    $_SESSION['vehicle_year'] = $vehicle_year;
    $_SESSION['vehicle_type'] = "RV";
    $_SESSION['ship_date'] = $ship_date;
    $_SESSION['vehicle_operable'] = $vehicle_operable;
    $_SESSION['transport_type'] = $transport_type === 1 ? 'Open' : 'Enclosed';

    // Convert data to JSON
    $json = json_encode($data);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - JSON payload: $json\n", FILE_APPEND);

    // Send API Request
    $ch = curl_init("https://api.batscrm.com/leads");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $json);

    $result = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    if ($httpCode === 200) {
        // Try to parse the response to get lead_id
        $lead_id = null;
        $responseData = json_decode($result, true);
        
        if (json_last_error() === JSON_ERROR_NONE && isset($responseData['lead_id'])) {
            $lead_id = $responseData['lead_id'];
        } elseif (preg_match('/OK, Lead :(\d+)/', $result, $matches)) {
            $lead_id = $matches[1];
        } else {
            // Handle the new API response format where no ID is provided
            if (strpos($result, 'OK, Lead') !== false || strpos($result, 'OK') !== false) {
                // Generate a unique lead ID for successful submissions
                $lead_id = 'LEAD-' . date('Ymd') . '-' . uniqid();
            } else {
                // For other cases, use temporary ID
                $lead_id = 'TEMP-' . uniqid();
            }
        }

        $_SESSION['lead_id'] = $lead_id;
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Success! Lead ID: $lead_id\n", FILE_APPEND);
        
        // Return success response
        echo json_encode([
            'success' => true,
            'message' => 'Order sent successfully!',
            'lead_id' => $lead_id,
            'redirect' => 'thank-you.php'
        ]);
    } else {
        // On error, generate a temporary ID but store the error
        $_SESSION['lead_id'] = 'ERR-' . uniqid();
        $_SESSION['api_error'] = "HTTP $httpCode: " . $result;
        error_log("BATS CRM API Error for RV: HTTP $httpCode - " . $result);
        
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error! Generated temp ID: " . $_SESSION['lead_id'] . "\n", FILE_APPEND);
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Error details: HTTP $httpCode - " . $result . "\n", FILE_APPEND);
        
        // Return error response
        echo json_encode([
            'success' => false,
            'message' => 'Error sending order. Please try again.',
            'error' => "HTTP $httpCode: " . $result
        ]);
    }
    exit;
} else {
    // If no data in request, return error
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - No data in request. Method: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - Request data: " . print_r($_REQUEST, true) . "\n", FILE_APPEND);
    
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request: No form data received'
    ]);
    exit;
}
?>
