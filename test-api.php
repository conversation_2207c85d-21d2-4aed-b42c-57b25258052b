<?php
/**
 * Test script for BATS CRM API Lead Submission
 * This script helps debug API responses and lead ID extraction
 */

// Test data - modify as needed
$testData = [
    "AuthKey" => "23d66938-96a5-4ad4-b1b5-122c12a307ea", // Sandbox Key
    "first_name" => "Test",
    "last_name" => "User",
    "email" => "<EMAIL>",
    "phone" => "(*************",
    "comment_from_shipper" => "Test submission",
    "origin_city" => "Lake Shore",
    "origin_state" => "MD",
    "origin_postal_code" => "21122",
    "origin_country" => "US",
    "destination_city" => "Baltimore",
    "destination_state" => "MD",
    "destination_postal_code" => "21224",
    "destination_country" => "US",
    "ship_date" => date("m/d/Y"),
    "transport_type" => 1,
    "Vehicles" => [
        [
            "vehicle_inop" => 0,
            "vehicle_make" => "Bentley",
            "vehicle_model" => "Continental",
            "vehicle_model_year" => 2023,
            "vehicle_type" => "Car"
        ]
    ]
];

echo "<h1>BATS CRM API Test</h1>";
echo "<h2>Test Data:</h2>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

$json = json_encode($testData);

// Send API Request
$ch = curl_init("https://api.batscrm.com/leads");
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($json)
]);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $json);

$result = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);
curl_close($ch);

echo "<h2>API Response:</h2>";
echo "<p><strong>HTTP Code:</strong> $httpCode</p>";

if ($curlError) {
    echo "<p><strong>cURL Error:</strong> $curlError</p>";
}

echo "<p><strong>Raw Response:</strong></p>";
echo "<pre>" . htmlspecialchars($result) . "</pre>";

// Test lead ID extraction
echo "<h2>Lead ID Extraction Test:</h2>";

$lead_id = null;
$extraction_method = 'none';

// First try JSON decode
$responseData = json_decode($result, true);
if (json_last_error() === JSON_ERROR_NONE && is_array($responseData)) {
    echo "<p><strong>JSON Decode:</strong> Success</p>";
    echo "<pre>" . json_encode($responseData, JSON_PRETTY_PRINT) . "</pre>";
    
    // Try multiple possible JSON structures
    $lead_id = $responseData['lead_id'] ?? 
              $responseData['data']['lead_id'] ?? 
              $responseData['id'] ?? 
              $responseData['leadId'] ?? 
              $responseData['LeadId'] ?? 
              $responseData['LEAD_ID'] ?? null;
    
    if ($lead_id) {
        $extraction_method = 'json';
        echo "<p><strong>Lead ID from JSON:</strong> $lead_id</p>";
    }
} else {
    echo "<p><strong>JSON Decode:</strong> Failed - " . json_last_error_msg() . "</p>";
}

// If JSON decode failed or didn't contain lead_id, try text parsing
if (!$lead_id) {
    echo "<p><strong>Trying text parsing...</strong></p>";

    // First check for the specific "ID no longer provided" response
    if (preg_match('/OK,\s*Lead\s*:\s*ID\s*no\s*longer\s*provided/i', $result)) {
        $api_success = true;
        $extraction_method = 'API accepted but no ID provided';
        echo "<p><strong>✅ API Response Recognized:</strong> Lead accepted but ID no longer provided</p>";
    } else {
        // Try other patterns for different response formats
        $patterns = [
            '/OK,\s*Lead\s*:?\s*(\d+)/i' => 'OK, Lead pattern',
            '/Lead\s*(?:ID)?[:\s]*(\d+)/i' => 'Lead ID pattern',
            '/ID[:\s]*(\d+)/i' => 'ID pattern',
            '/Success.*?(\d{6,})/i' => 'Success pattern',
            '/Created.*?(\d{6,})/i' => 'Created pattern',
            '/(\d{6,})/' => 'Any 6+ digit number'
        ];

        foreach ($patterns as $pattern => $description) {
            if (preg_match($pattern, $result, $matches)) {
                $lead_id = $matches[1];
                $extraction_method = $description;
                echo "<p><strong>Lead ID extracted with '$description':</strong> $lead_id</p>";
                break;
            } else {
                echo "<p>Pattern '$description' - No match</p>";
            }
        }
    }
}

// Final result
echo "<h2>Final Result:</h2>";
if ($lead_id) {
    echo "<p><strong>✅ Lead ID Found:</strong> $lead_id</p>";
    echo "<p><strong>Extraction Method:</strong> $extraction_method</p>";
} elseif (isset($api_success) && $api_success) {
    echo "<p><strong>✅ API Accepted Lead:</strong> No ID provided but lead was accepted</p>";
    echo "<p><strong>Extraction Method:</strong> $extraction_method</p>";
    $timestamp = date('YmdHis');
    $random = substr(md5(uniqid(mt_rand(), true)), 0, 8);
    echo "<p><strong>Would generate accepted ID:</strong> ACCEPTED-$timestamp-$random</p>";
} else {
    echo "<p><strong>❌ No Lead ID Found</strong></p>";
    $timestamp = date('YmdHis');
    $random = substr(md5(uniqid(mt_rand(), true)), 0, 8);
    echo "<p><strong>Would generate temporary ID:</strong> TEMP-$timestamp-$random</p>";
}

// Additional debugging info
echo "<h2>Additional Debug Info:</h2>";
echo "<p><strong>Response Length:</strong> " . strlen($result) . " characters</p>";
echo "<p><strong>Response Type:</strong> " . (is_numeric($result) ? 'Numeric' : (json_decode($result) ? 'JSON' : 'Text')) . "</p>";

// Check for common response patterns
$commonPatterns = [
    'OK' => 'Contains "OK"',
    'Lead' => 'Contains "Lead"',
    'Error' => 'Contains "Error"',
    'Success' => 'Contains "Success"',
    'Failed' => 'Contains "Failed"'
];

echo "<p><strong>Response Content Analysis:</strong></p>";
foreach ($commonPatterns as $pattern => $description) {
    $found = stripos($result, $pattern) !== false;
    echo "<p>$description: " . ($found ? '✅ Yes' : '❌ No') . "</p>";
}
?>
