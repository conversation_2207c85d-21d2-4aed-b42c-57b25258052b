<?php
// Always use light theme (dark mode disabled)
$stylesheet = "public/css/thanks.css";

// Enable strict error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Set proper headers FIRST
    header("Content-Type: application/json");
    
    $logFile = dirname(__DIR__) . '/form_submissions.log';
    
    try {
        // Create log file if doesn't exist
        if (!file_exists($logFile)) {
            file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Log file created\n");
            chmod($logFile, 0666);
        }

        // Get input data
        $postData = $_POST ?: json_decode(file_get_contents('php://input'), true);
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Raw input:\n" . print_r($postData, true) . "\n", FILE_APPEND);

        // Validate required fields
        $requiredFields = ['name', 'email', 'phone', 'transport_from', 'transport_to', 
                         'transport_type', 'vehicle_year', 'vehicle_brand', 'vehicle_model', 'vehicle_operable'];
        $missingFields = array_filter($requiredFields, fn($field) => empty($postData[$field]));

        if (!empty($missingFields)) {
            throw new Exception('Missing required fields: ' . implode(', ', $missingFields), 400);
        }

        // Prepare API data
        $data = [
            "AuthKey" => "23d66938-96a5-4ad4-b1b5-122c12a307ea",
            "first_name" => $postData['name'],
            "last_name" => "Customer",
            "email" => $postData['email'],
            "phone" => preg_replace('/[^0-9]/', '', $postData['phone']),
            "ship_date" => date('m/d/Y'),
            "transport_type" => $postData['transport_type'] === 'Enclosed' ? 2 : 1,
            "origin_postal_code" => "00000",
            "origin_city" => "Unknown",
            "origin_state" => "Unknown",
            "origin_country" => "US",
            "destination_postal_code" => "00000",
            "destination_city" => "Unknown",
            "destination_state" => "Unknown",
            "destination_country" => "US",
            "Vehicles" => [[
                "vehicle_inop" => $postData['vehicle_operable'] === 'no' ? 1 : 0,
                "vehicle_type" => 1,
                "vehicle_make" => $postData['vehicle_brand'],
                "vehicle_model" => $postData['vehicle_model'],
                "vehicle_model_year" => $postData['vehicle_year'],
                "vehicle_length" => 15,
                "vehicle_width" => 6,
                "vehicle_height" => 5
            ]]
        ];

        // Log prepared data
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] Prepared data:\n" . json_encode($data, JSON_PRETTY_PRINT) . "\n", FILE_APPEND);

        // Send to API
        $ch = curl_init("https://api.batscrm.com/leads");
        curl_setopt_array($ch, [
            CURLOPT_POST => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                "Content-Type: application/json",
                "Accept: application/json"
            ],
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HEADER => true
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($response === false) {
            throw new Exception('CURL Error: ' . curl_error($ch), 500);
        }

        // Parse response
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $body = substr($response, $headerSize);
        curl_close($ch);

        // Log response
        file_put_contents($logFile, "[" . date('Y-m-d H:i:s') . "] API Response ($httpCode):\n$body\n\n", FILE_APPEND);

        // Validate response
        if ($httpCode !== 200) {
            throw new Exception("API returned status $httpCode", $httpCode);
        }

        $responseData = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Invalid JSON response from API", 500);
        }

        // Successful response - return simple success
        echo json_encode(['success' => true]);
        exit;
        
    } catch (Exception $e) {
        $errorData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ];
        
        file_put_contents($logFile, "ERROR:\n" . json_encode($errorData, JSON_PRETTY_PRINT) . "\n\n", FILE_APPEND);
        
        http_response_code($e->getCode() ?: 500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'error_code' => $e->getCode()
        ]);
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Your Quote Request Is in Motion!</title>
    <link rel="stylesheet" type="text/css" href="public/css/index.css" />
    <link rel="stylesheet" type="text/css" href="public/css/styles.css" />
    <link rel="stylesheet" type="text/css" href="public/css/components.css" />
    <link rel="stylesheet" type="text/css" href="public/css/main.css" />
    <link rel="icon" type="image/x-icon" href="public/favicon.ico">
    <link rel="stylesheet" type="text/css" href="<?php echo $stylesheet; ?>" />
    <script defer src='public/js/menu.js'></script>
    
    <!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-NGJGCPBZ');</script>
<!-- End Google Tag Manager -->

</head>
<body>
    <div class="home-p">
        <?php include 'includes/header.php' ?>
    </div>

    <div class="thank-you-page">
        <div class="container">
            <div class="thank-you">
                <!-- Success Icon SVG with Animation -->
                <div class="success-icon-container">
                    <svg class="success-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                        <circle class="success-circle" cx="50" cy="50" r="45" fill="none" stroke="#cda565"/>
                        <path class="success-checkmark" d="M25 50 L40 65 L75 30" fill="none" stroke="#cda565" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <h1>Thanks for Booking!</h1>
                <p>Your order is being processed. We’ll contact you shortly to confirm the details..</p>
                <!--<p>Expect a text or call shortly. Need answers ASAP? Call <a href="tel:+18778788008" class="phone-number">(*************</a> now!</p>-->
            </div>
            <br>
            <a href="/" class="back-button">Back to Home</a><br><br>
            <div class="timer" id="timer">Redirecting in 5 seconds...</div>
        </div>
    </div>
    
    <!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-NGJGCPBZ"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <?php include 'includes/footer.php' ?>

<style>
    /*.thank-you-page{*/
    /*    padding-bottom: 200px !important;*/
    /*}*/
</style>
<script defer src='public/js/thanks.js'></script>
</body>
</html>
